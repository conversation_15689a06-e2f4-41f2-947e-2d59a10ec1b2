{"name": "qbraid-mcp-server", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "bin": {"weather": "./build/index.js"}, "scripts": {"build": "tsc && chmod 755 build/index.js", "start": "node build/index.js", "dev": "npm run build && npm run start", "clean": "rm -rf build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.17.2", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^24.2.1", "typescript": "^5.9.2"}, "files": ["build"]}