import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { z } from "zod";

/**
 * qBraid MCP Server
 *
 * This server provides read-only access to qBraid's quantum computing platform API.
 * It exposes tools for managing quantum devices, jobs, organizations, billing, and more.
 */

// Configuration constants
const API_BASE_URL = "https://xyz.com/api";
const DEFAULT_HEADERS = { accept: "application/json" } as const;

/**
 * Whitelist of allowed API path prefixes for security.
 * Only GET requests to these paths are permitted.
 */
const ALLOWED_PATHS = [
    "/providers",
    "/providers/",
    "/public/verify-org/",
    "/orgs/get/",
    "/organizations/",
    "/device-access-requests",
    "/activity-logs",
    "/v2/quantum-devices",
    "/quantum-jobs/cost-estimate",
    "/quantum-jobs/result/",
    "/billing/products",
    "/billing/credits/get-user-credits",
] as const;

// Global authentication token storage
let globalIdToken: string | null = null;

// Common schemas for reuse
const PaginationSchema = {
    page: z.number().min(1).default(1),
    limit: z.number().min(1).max(100).default(20),
};

/**
 * Utility function to create authorized headers using global idtoken
 */
function createHeaders(): Record<string, string> {
    const headers: Record<string, string> = { ...DEFAULT_HEADERS };
    if (globalIdToken) {
        headers.idtoken = globalIdToken;
    }
    return headers;
}

/**
 * Prompt user for idtoken at startup
 */
function promptForIdToken(): Promise<string> {
    return new Promise((resolve) => {
        const readline = require("readline");
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
        });

        rl.question("Please enter your qBraid idtoken: ", (token: string) => {
            rl.close();
            resolve(token.trim());
        });
    });
}

/**
 * Utility function to handle API responses consistently
 */
async function handleApiResponse(
    response: Response
): Promise<{ content: Array<{ type: "text"; text: string }> }> {
    const body = await response.text();
    const prefix = response.ok ? "OK" : `HTTP ${response.status}`;
    return {
        content: [
            {
                type: "text",
                text: `${prefix}: ${body}`,
            },
        ],
    };
}

/**
 * Utility function to format JSON responses
 */
function formatJsonResponse(data: any): {
    content: Array<{ type: "text"; text: string }>;
} {
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
}

/**
 * Create and configure the MCP server instance
 */
const server = new McpServer({
    name: "qbraid-mcp-server",
    version: "0.0.1",
    capabilities: {
        resources: {},
        tools: {},
        prompts: {},
    },
});

// ============================================================================
// GENERIC API TOOL
// ============================================================================

/**
 * Generic API GET tool with path whitelisting for security
 */
server.tool(
    "api-get",
    "Execute read-only GET requests against whitelisted qBraid API endpoints",
    {
        path: z
            .string()
            .describe("API path (e.g., '/providers' or '/quantum-devices')"),
        query: z
            .record(z.string())
            .optional()
            .describe("Query parameters as key-value pairs"),
    },
    {
        title: "Generic API GET Request",
        description:
            "Secure read-only access to whitelisted qBraid API endpoints",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: true,
    },
    async ({ path, query }) => {
        // Normalize and validate the path
        const normalizedPath = path.startsWith("/") ? path : `/${path}`;

        // Security check: ensure path is whitelisted
        if (
            !ALLOWED_PATHS.some((allowedPath) =>
                normalizedPath.startsWith(allowedPath)
            )
        ) {
            return {
                content: [
                    {
                        type: "text",
                        text: "Error: Path not allowed. Check ALLOWED_PATHS configuration.",
                    },
                ],
            };
        }

        // Build the full URL
        const url = new URL(normalizedPath, API_BASE_URL);

        // Add query parameters if provided
        if (query) {
            Object.entries(query).forEach(([key, value]) => {
                url.searchParams.set(key, value);
            });
        }

        try {
            const response = await fetch(url, {
                headers: createHeaders(),
                method: "GET",
            });
            return await handleApiResponse(response);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error: ${
                            error instanceof Error
                                ? error.message
                                : "Unknown error"
                        }`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// PROVIDER MANAGEMENT TOOLS
// ============================================================================

/**
 * List all available quantum computing providers
 */
server.tool(
    "list_providers",
    "Retrieve a list of all quantum computing providers available on qBraid",
    {},
    {
        title: "List Quantum Providers",
        description:
            "View all available quantum computing providers on the qBraid platform",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/providers`, {
                headers: createHeaders(),
            });
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching providers: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get ownership information for a specific provider
 */
server.tool(
    "get_provider_ownership",
    "Retrieve ownership details for a specific quantum computing provider",
    {
        providerId: z.string().describe("Unique identifier of the provider"),
    },
    {
        title: "Get Provider Ownership",
        description:
            "View ownership and management details for a quantum computing provider",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ providerId }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/providers/${providerId}/ownership`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching provider ownership: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// ORGANIZATION MANAGEMENT TOOLS
// ============================================================================

/**
 * Verify organization invitation (public endpoint, no auth required)
 */
// server.tool(
//     "verify_org_invite",
//     "Verify an organization invitation using ID and email (public endpoint)",
//     {
//         id: z.string().describe("Organization invitation ID"),
//         email: z.string().email().describe("Email address to verify"),
//     },
//     {
//         title: "Verify Organization Invitation",
//         description:
//             "Check validity of an organization invitation (public endpoint)",
//         readOnlyHint: true,
//         destructiveHint: false,
//         idempotent: true,
//         openWorldHint: false,
//     },
//     async ({ id, email }) => {
//         try {
//             const url = `${API_BASE_URL}/public/verify-org/${id}?email=${encodeURIComponent(
//                 email
//             )}`;
//             const response = await fetch(url, { headers: DEFAULT_HEADERS });
//             const data = await response.json();
//             return formatJsonResponse(data);
//         } catch (error) {
//             return {
//                 content: [
//                     {
//                         type: "text",
//                         text: `Error verifying organization invite: ${error}`,
//                     },
//                 ],
//             };
//         }
//     }
// );

/**
 * List organizations the authenticated user belongs to
 */
server.tool(
    "list_user_orgs",
    "Retrieve organizations that the authenticated user is a member of",
    {
        ...PaginationSchema,
    },
    {
        title: "List User Organizations",
        description:
            "View organizations that the authenticated user belongs to",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ page, limit }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/orgs/get/${page}/${limit}`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching user organizations: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get organization details by name
 */
server.tool(
    "get_org_by_name",
    "Retrieve detailed information about an organization by its name",
    {
        name: z.string().describe("Organization name"),
    },
    {
        title: "Get Organization by Name",
        description:
            "Retrieve detailed organization information using organization name",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ name }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/orgs/get/${encodeURIComponent(name)}`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching organization by name: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get device access information for an organization
 */
server.tool(
    "get_org_device_access",
    "Retrieve device access permissions and details for a specific organization",
    {
        orgId: z.string().describe("Organization ID"),
    },
    {
        title: "Get Organization Device Access",
        description:
            "View device access permissions for a specific organization",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ orgId }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/organizations/${orgId}/device-access`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching organization device access: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// DEVICE ACCESS REQUEST TOOLS
// ============================================================================

/**
 * List device access requests with optional filtering
 */
server.tool(
    "list_device_access_requests",
    "Retrieve device access requests with optional status and organization filters",
    {
        status: z
            .string()
            .optional()
            .describe(
                "Filter by request status (e.g., 'pending', 'approved', 'denied')"
            ),
        organizationId: z
            .string()
            .optional()
            .describe("Filter by organization ID"),
    },
    {
        title: "List Device Access Requests",
        description: "View device access requests with optional filtering",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ status, organizationId }) => {
        try {
            const url = new URL(`${API_BASE_URL}/device-access-requests`);
            if (status) url.searchParams.set("status", status);
            if (organizationId)
                url.searchParams.set("organizationId", organizationId);

            const response = await fetch(url, {
                headers: createHeaders(),
            });
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching device access requests: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get details of a specific device access request
 */
server.tool(
    "get_device_access_request",
    "Retrieve detailed information about a specific device access request",
    {
        requestId: z.string().describe("Device access request ID"),
    },
    {
        title: "Get Device Access Request Details",
        description:
            "View detailed information about a specific device access request",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ requestId }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/device-access-requests/${requestId}`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching device access request: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// ACTIVITY LOGS TOOLS
// ============================================================================

/**
 * Get activity logs with comprehensive filtering and pagination
 */
server.tool(
    "get_activity_logs",
    "Retrieve activity logs with advanced filtering options and pagination",
    {
        organizationId: z
            .string()
            .optional()
            .describe("Filter by organization ID"),
        userId: z.string().optional().describe("Filter by user ID"),
        action: z.string().optional().describe("Filter by action type"),
        resourceType: z.string().optional().describe("Filter by resource type"),
        status: z.string().optional().describe("Filter by status"),
        startDate: z
            .string()
            .optional()
            .describe("Start date for filtering (ISO format)"),
        endDate: z
            .string()
            .optional()
            .describe("End date for filtering (ISO format)"),
        page: z
            .number()
            .min(1)
            .default(1)
            .describe("Page number for pagination"),
        resultsPerPage: z
            .number()
            .min(1)
            .max(100)
            .default(20)
            .describe("Number of results per page"),
    },
    {
        title: "Get Activity Logs",
        description:
            "View system activity logs with advanced filtering and pagination",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async (params) => {
        try {
            const url = new URL(`${API_BASE_URL}/activity-logs`);

            // Add all non-undefined parameters to the URL
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && key !== "authToken") {
                    url.searchParams.set(key, value.toString());
                }
            });

            const response = await fetch(url, {
                headers: createHeaders(),
            });
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching activity logs: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// QUANTUM DEVICES TOOLS
// ============================================================================

/**
 * List quantum devices with filtering options
 */
server.tool(
    "list_devices",
    "Retrieve quantum devices with optional filtering by provider, type, status, and availability",
    {
        provider: z.string().optional().describe("Filter by provider name"),
        type: z.string().optional().describe("Filter by device type"),
        status: z.string().optional().describe("Filter by device status"),
        isAvailable: z
            .boolean()
            .optional()
            .describe("Filter by availability status"),
    },
    {
        title: "List Quantum Devices",
        description: "View available quantum devices with filtering options",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ provider, type, status, isAvailable }) => {
        try {
            const url = new URL(`${API_BASE_URL}/v2/quantum-devices`);
            if (provider) url.searchParams.set("provider", provider);
            if (type) url.searchParams.set("type", type);
            if (status) url.searchParams.set("status", status);
            if (isAvailable !== undefined)
                url.searchParams.set("isAvailable", isAvailable.toString());

            const response = await fetch(url, {
                headers: createHeaders(),
            });
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching quantum devices: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * List all quantum devices across all providers
 */
server.tool(
    "list_all_devices",
    "Retrieve all quantum devices available across all providers",
    {},
    {
        title: "List All Quantum Devices",
        description: "View all quantum devices across all providers",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async () => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/v2/quantum-devices/all`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching all quantum devices: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get provider information for quantum devices
 */
server.tool(
    "get_provider_info",
    "Retrieve detailed information about a quantum computing provider",
    {
        provider: z.string().describe("Provider name or identifier"),
    },
    {
        title: "Get Provider Information",
        description:
            "View detailed information about a quantum computing provider",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ provider }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/v2/quantum-devices/providers/${provider}`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching provider info: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// QUANTUM JOBS TOOLS
// ============================================================================

/**
 * Estimate the cost of running a quantum job
 */
server.tool(
    "estimate_job_cost",
    "Calculate the estimated cost for running a quantum job with specified parameters",
    {
        qbraidDeviceId: z.string().describe("qBraid device identifier"),
        shots: z.number().min(1).describe("Number of measurement shots"),
        minutes: z.number().min(1).describe("Expected runtime in minutes"),
    },
    {
        title: "Estimate Quantum Job Cost",
        description: "Calculate estimated cost for running a quantum job",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ qbraidDeviceId, shots, minutes }) => {
        try {
            const url = new URL(`${API_BASE_URL}/quantum-jobs/cost-estimate`);
            url.searchParams.set("qbraidDeviceId", qbraidDeviceId);
            url.searchParams.set("shots", shots.toString());
            url.searchParams.set("minutes", minutes.toString());

            const response = await fetch(url, {
                headers: createHeaders(),
            });
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error estimating job cost: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get the result of a completed quantum job
 */
server.tool(
    "get_job_result",
    "Retrieve the results and output data from a completed quantum job",
    {
        qbraidJobId: z.string().describe("qBraid job identifier"),
    },
    {
        title: "Get Quantum Job Result",
        description: "Retrieve results and output from a completed quantum job",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async ({ qbraidJobId }) => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/quantum-jobs/result/${qbraidJobId}`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching job result: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// BILLING TOOLS
// ============================================================================

/**
 * List available billing products and pricing information
 */
server.tool(
    "list_billing_products",
    "Retrieve available billing products and their pricing details",
    {},
    {
        title: "List Billing Products",
        description: "View available billing products and pricing information",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/billing/products`, {
                headers: createHeaders(),
            });
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching billing products: ${error}`,
                    },
                ],
            };
        }
    }
);

/**
 * Get the authenticated user's current credit balance
 */
server.tool(
    "get_user_credits",
    "Retrieve the current credit balance for the authenticated user",
    {},
    {
        title: "Get User Credits",
        description: "View current credit balance for the authenticated user",
        readOnlyHint: true,
        destructiveHint: false,
        idempotent: true,
        openWorldHint: false,
    },
    async () => {
        try {
            const response = await fetch(
                `${API_BASE_URL}/billing/credits/get-user-credits`,
                {
                    headers: createHeaders(),
                }
            );
            const data = await response.json();
            return formatJsonResponse(data);
        } catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching user credits: ${error}`,
                    },
                ],
            };
        }
    }
);

// ============================================================================
// SERVER INITIALIZATION
// ============================================================================

/**
 * Initialize and start the MCP server with HTTP transport
 */
function main(): void {
    try {
        const transport = new StreamableHTTPServerTransport({
            sessionIdGenerator: undefined,
        });

        server.connect(transport);
        console.log("qBraid MCP Server started successfully");
    } catch (error) {
        console.error("Failed to start qBraid MCP Server:", error);
        process.exit(1);
    }
}

// Start the server
main();
